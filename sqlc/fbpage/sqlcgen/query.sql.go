// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: query.sql

package fbpage_sqlcgen

import (
	"context"
	"database/sql"
	"time"

	"github.com/lib/pq"
	"github.com/sqlc-dev/pqtype"
)

const createPage = `-- name: CreatePage :one
INSERT INTO pages (id, name, access_token, app_secret)
VALUES ($1, $2, $3, $4)
ON CONFLICT (id) DO UPDATE
    SET name = EXCLUDED.name,
    access_token = EXCLUDED.access_token,
    app_secret = EXCLUDED.app_secret,
    updated_at = NOW()
RETURNING created_at, updated_at
`

type CreatePageParams struct {
	ID          string
	Name        string
	AccessToken string
	AppSecret   string
}

type CreatePageRow struct {
	CreatedAt time.Time
	UpdatedAt sql.NullTime
}

func (q *Queries) CreatePage(ctx context.Context, arg CreatePageParams) (CreatePageRow, error) {
	row := q.db.QueryRowContext(ctx, createPage,
		arg.ID,
		arg.Name,
		arg.AccessToken,
		arg.AppSecret,
	)
	var i CreatePageRow
	err := row.Scan(&i.CreatedAt, &i.UpdatedAt)
	return i, err
}

const createPosts = `-- name: CreatePosts :exec
INSERT INTO posts (id, text_content, url, attachments, page_id)
VALUES ($1, $2, $3, $4, $5)
ON CONFLICT (id) DO UPDATE
    SET text_content = EXCLUDED.text_content,
    url = EXCLUDED.url,
    attachments = EXCLUDED.attachments,
    updated_at = NOW()
RETURNING created_at, updated_at
`

type CreatePostsParams struct {
	ID          string
	TextContent sql.NullString
	Url         sql.NullString
	Attachments pqtype.NullRawMessage
	PageID      string
}

func (q *Queries) CreatePosts(ctx context.Context, arg CreatePostsParams) error {
	_, err := q.db.ExecContext(ctx, createPosts,
		arg.ID,
		arg.TextContent,
		arg.Url,
		arg.Attachments,
		arg.PageID,
	)
	return err
}

const deletePages = `-- name: DeletePages :exec
DELETE FROM pages WHERE id = ANY($1::text[])
`

func (q *Queries) DeletePages(ctx context.Context, dollar_1 []string) error {
	_, err := q.db.ExecContext(ctx, deletePages, pq.Array(dollar_1))
	return err
}

const getAccessToken = `-- name: GetAccessToken :one
SELECT access_token FROM pages WHERE id = $1
`

func (q *Queries) GetAccessToken(ctx context.Context, id string) (string, error) {
	row := q.db.QueryRowContext(ctx, getAccessToken, id)
	var access_token string
	err := row.Scan(&access_token)
	return access_token, err
}

const getPosts = `-- name: GetPosts :many
SELECT id, text_content, url, attachments
FROM posts
WHERE page_id = $1
`

type GetPostsRow struct {
	ID          string
	TextContent sql.NullString
	Url         sql.NullString
	Attachments pqtype.NullRawMessage
}

func (q *Queries) GetPosts(ctx context.Context, pageID string) ([]GetPostsRow, error) {
	rows, err := q.db.QueryContext(ctx, getPosts, pageID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetPostsRow
	for rows.Next() {
		var i GetPostsRow
		if err := rows.Scan(
			&i.ID,
			&i.TextContent,
			&i.Url,
			&i.Attachments,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
