// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package fbpage_sqlcgen

import (
	"database/sql"
	"time"

	"github.com/sqlc-dev/pqtype"
)

type Page struct {
	ID          string
	Name        string
	AccessToken string
	AppSecret   string
	CreatedAt   time.Time
	UpdatedAt   sql.NullTime
}

type Post struct {
	ID          string
	TextContent sql.NullString
	Url         sql.NullString
	Attachments pqtype.NullRawMessage
	CreatedAt   time.Time
	UpdatedAt   sql.NullTime
	PageID      string
}
