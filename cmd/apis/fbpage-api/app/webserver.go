package app

import (
	"net/http"
	"time"

	"github.com/justinas/alice"
	"github.com/spf13/viper"
	"gitlab.com/a7923/athena-go/cmd/apis/fbpage-api/handlers"
	"gitlab.com/a7923/athena-go/internal"
	"gitlab.com/a7923/athena-go/internal/fbpage"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/config"
	"gitlab.com/a7923/athena-go/pkg/micro/web"
	middleware "gitlab.com/a7923/athena-go/pkg/middlewares"
	"go-micro.dev/v5/client"
)

type Server struct {
	Name     string
	client   client.Client
	producer app.PublisherInterface
}

func NewServer(publisher app.PublisherInterface) *Server {
	return &Server{
		Name:     "fbpage",
		producer: publisher,
	}
}

func (s *Server) SetGRPCClient(client client.Client) {
	s.client = client
}

func (s *Server) GetBasePath() string {
	return "/fbpages"
}

func (s *Server) GetName() string {
	return s.Name
}

func (s *Server) GetRoutes() web.Routes {
	mdws := []alice.Constructor{}

	if viper.GetBool("logging.enable") {
		mdws = append(mdws, middleware.LoggingMiddleware)
	}

	fbpageClient := internal.CreateFBPageClient(nil)
	fbAPIBaseURL := config.ViperGetStringWithDefault("fbpage.base_url", "https://graph.facebook.com/v22.0")
	pageFetcher := fbpage.NewPageFetcher(&fbpage.PageFetcherConfig{
		BaseURL: fbAPIBaseURL,
		Client: &http.Client{
			Timeout: 90 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 100,
				IdleConnTimeout:     90 * time.Second,
			},
		},
	})

	appSecret := viper.GetString("fbpage.app_secret")
	if appSecret == "" {
		panic("fbpage.app_secret is not set")
	}

	verifyToken := viper.GetString("fbpage.verify_token")
	if verifyToken == "" {
		panic("fbpage.verify_token is not set")
	}

	return []web.Route{
		{
			Name:    "Register Page",
			Method:  http.MethodPost,
			Pattern: "/pages",
			Handler: &handlers.RegisterPageHandler{
				PageClient:  fbpageClient,
				PageFetcher: pageFetcher,
				AppSecret:   appSecret,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: false,
			},
			Timeout: 30000, // 30 seconds
		},
		{
			Name:    "Delete Pages",
			Method:  http.MethodDelete,
			Pattern: "/pages",
			Handler: &handlers.DeletePagesHandler{
				PageClient: fbpageClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: false,
			},
			Timeout: 30000, // 30 seconds
		},
		{
			Name:    "Messenger Webhook Verify",
			Method:  http.MethodGet,
			Pattern: "/pages/webhook",
			Handler: &handlers.MessengerWebhookHandler{
				VerifyToken: verifyToken,
				AppSecret:   appSecret,
				Publisher:   s.producer,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: false,
			},
			Timeout: 30000, // 30 seconds
		},
		{
			Name:    "Messenger Webhook Handler",
			Method:  http.MethodPost,
			Pattern: "/pages/webhook",
			Handler: &handlers.MessengerWebhookHandler{
				VerifyToken: verifyToken,
				AppSecret:   appSecret,
				Publisher:   s.producer,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: false,
			},
			Timeout: 60000, // 60 seconds
		},
		{
			Name:    "List Pages",
			Method:  http.MethodGet,
			Pattern: "/pages",
			Handler: &handlers.ListPagesHandler{
				PageClient: fbpageClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: false,
			},
			Timeout: 30000, // 30 seconds
		},
		{
			Name:    "List Posts",
			Method:  http.MethodGet,
			Pattern: "/pages/{id}/posts",
			Handler: &handlers.ListPostsHandler{
				PageClient: fbpageClient,
			},
			Middlewares: mdws,
			AuthInfo: web.AuthInfo{
				Enable: false,
			},
			Timeout: 60000, // 60 seconds
		},
	}
}
