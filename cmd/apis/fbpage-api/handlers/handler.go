package handlers

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"slices"
	"time"

	"github.com/tidwall/gjson"
	"gitlab.com/a7923/athena-go/internal/fbpage"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/messenger"
	transhttp "gitlab.com/a7923/athena-go/pkg/transport"
	"gitlab.com/a7923/athena-go/pkg/utils"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

const FBPageExchangeName = "fb.page.events"

type RegisterPageRequest struct {
	AccessToken string `json:"access_token"`
}

type RegisterPageHandler struct {
	PageClient  services.FBPageService
	PageFetcher *fbpage.PageFetcher
	AppSecret   string
}

func (h *RegisterPageHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	atlogger := logger.FromCtx(r.Context())

	start := time.Now()
	defer func() {
		atlogger.Debugw("Processed request",
			"url", r.URL.Path,
			"took", time.Since(start).Milliseconds())
	}()

	// Decode request body
	var httpRegisterPageReq RegisterPageRequest
	err := json.NewDecoder(r.Body).Decode(&httpRegisterPageReq)
	if err != nil {
		atlogger.Errorw("Failed to decode request body",
			"error", err,
			"url", r.URL.Path,
		)
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to decode request body: %v", err))
		return
	}

	// Fetch page info from Facebook
	// Fetched fields: id, name
	pageInfo, err := h.PageFetcher.FetchPageInfo(r.Context(), httpRegisterPageReq.AccessToken)
	if err != nil {
		atlogger.Errorw("Failed to fetch page info",
			"error", err,
			"url", r.URL.Path,
		)
		transhttp.RespondError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to fetch page info: %v", err))
		return
	}

	// Send register page request to FBPage service in order to insert page info to the database
	grpcRegisterPageReq := &services.RegisterPageRequest{
		PageId:          pageInfo.ID,
		PageName:        pageInfo.Name,
		PageAccessToken: httpRegisterPageReq.AccessToken,
		AppSecret:       h.AppSecret,
	}
	resp, err := h.PageClient.RegisterPage(r.Context(), grpcRegisterPageReq)
	if err != nil {
		atlogger.Errorw("Failed to register page",
			"error", err,
		)
		transhttp.RespondError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to register page: %v", err))
		return
	}

	transhttp.RespondJSON(w, http.StatusOK, resp)
}

type DeletePagesRequest struct {
	PageIDs []string `json:"page_ids"`
}

type DeletePagesHandler struct {
	PageClient services.FBPageService
}

func (h *DeletePagesHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	atlogger := logger.FromCtx(r.Context())

	start := time.Now()
	defer func() {
		atlogger.Debugw("Processed request",
			"url", r.URL.Path,
			"took", time.Since(start).Milliseconds())
	}()

	// Decode request body
	var httpDeletePagesReq DeletePagesRequest
	err := json.NewDecoder(r.Body).Decode(&httpDeletePagesReq)
	if err != nil {
		atlogger.Errorw("Failed to decode request body",
			"error", err,
		)
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("failed to decode request body: %v", err))
		return
	}

	// Send delete pages request to FBPage service in order to delete page from the database
	grpcDeletePagesReq := &services.BulkDeletePagesRequest{
		PageIds: httpDeletePagesReq.PageIDs,
	}
	resp, err := h.PageClient.BulkDeletePages(r.Context(), grpcDeletePagesReq)
	if err != nil {
		atlogger.Errorw("Failed to delete pages",
			"error", err,
			"pageIDs", httpDeletePagesReq.PageIDs,
		)
		transhttp.RespondError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to delete pages: %v", err))
		return
	}

	transhttp.RespondJSON(w, http.StatusOK, resp)
}

type MessengerWebhookHandler struct {
	VerifyToken string
	AppSecret   string
	Publisher   app.PublisherInterface
}

func (h *MessengerWebhookHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	atlogger := logger.FromCtx(r.Context())

	start := time.Now()
	defer func() {
		atlogger.Debugw("Processed request",
			"url", r.URL.Path,
			"took", time.Since(start).Milliseconds())
	}()

	switch r.Method {
	case http.MethodGet:
		h.webhookVerify(w, r)
	case http.MethodPost:
		h.webhookHandler(w, r)
	default:
		w.WriteHeader(http.StatusMethodNotAllowed)
	}
}

func (h *MessengerWebhookHandler) webhookVerify(w http.ResponseWriter, r *http.Request) {
	atlogger := logger.FromCtx(r.Context())

	u := r.URL
	verifyToken := u.Query().Get("hub.verify_token")
	mode := u.Query().Get("hub.mode")
	challenge := u.Query().Get("hub.challenge")

	if mode == "subscribe" && verifyToken == h.VerifyToken {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(challenge))
	} else {
		atlogger.Errorw("Webhook verification failed",
			"mode", mode,
			"verifyToken", verifyToken,
			"challenge", challenge)
		w.WriteHeader(http.StatusForbidden)
	}
}

func (h *MessengerWebhookHandler) webhookHandler(w http.ResponseWriter, r *http.Request) {
	atlogger := logger.FromCtx(r.Context())

	body, err := io.ReadAll(r.Body)
	if err != nil {
		atlogger.Errorw("Failed to read request body",
			"error", err,
		)
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	defer r.Body.Close()

	// Attach page id to logger
	pageID := gjson.GetBytes(body, "entry.0.id").String()
	if pageID == "" {
		atlogger.Errorw("Page ID is empty")
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	atlogger.SugaredLogger = atlogger.With("page_id", pageID)

	signatureHeader := r.Header.Get("X-Hub-Signature-256")
	if !isValidSignature(signatureHeader, body, h.AppSecret) {
		atlogger.Errorw("Invalid signature",
			"signatureHeader", signatureHeader,
		)
		w.WriteHeader(http.StatusForbidden)
		return
	}

	w.WriteHeader(http.StatusOK)
	atlogger.Infow("Received message",
		"body", string(body),
	)

	event := messenger.WebhookEvent{}
	if err := json.Unmarshal(body, &event); err != nil {
		atlogger.Errorw("Failed to unmarshal request body",
			"error", err,
			"body", string(body),
		)
		return
	}

	for _, entry := range event.Entry {
		byteEntry := utils.ToJSONByte(entry)
		logger.AthenaLogger.Infow("Publishing message",
			"exchange", FBPageExchangeName,
			"routing_key", "messenger",
			"entry", string(byteEntry),
		)
		err := h.Publisher.PublishRoutingPersist(FBPageExchangeName, "messenger", byteEntry)
		if err != nil {
			atlogger.Errorw("Failed to publish message",
				"error", err,
				"exchange", FBPageExchangeName,
				"routing_key", "messenger",
				"entry", byteEntry,
			)
		}
	}
}

func isValidSignature(signatureHeader string, body []byte, appSecret string) bool {
	if len(signatureHeader) <= 7 {
		return false
	}

	actualSignature, err := hex.DecodeString(signatureHeader[7:])
	if err != nil {
		return false
	}

	mac := hmac.New(sha256.New, []byte(appSecret))
	mac.Write(body)
	expectedSignature := mac.Sum(nil)

	return hmac.Equal(expectedSignature, actualSignature)
}

type ListPagesResponse struct {
	Pages      []fbpage.Page     `json:"pages"`
	Pagination common.Pagination `json:"pagination"`
}

type ListPagesHandler struct {
	PageClient services.FBPageService
}

const (
	defaultFBPagePage      = 1
	defaultFBPageLimit     = 10
	defaultFBPageSortField = "name"
	defaultFBPageSortOrder = "asc"
)

var (
	mapFBPageSortFieldToDBField = map[string]string{
		"id":   "id",
		"name": "name",
	}
	allowedFBPageSortFields = []string{"id", "name"}
)

func (h *ListPagesHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	atlogger := logger.FromCtx(r.Context())

	start := time.Now()
	defer func() {
		atlogger.Debugw("Processed request",
			"url", r.URL.Path,
			"took", time.Since(start).Milliseconds())
	}()

	// Extract Pagination Parameters
	paginationParam, err := common.ExtractPaginationQueryParam(r, defaultFBPagePage, defaultFBPageLimit)
	if err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid pagination param: %v", err))
		return
	}

	// Extract Sorting Parameters
	sortByParam, err := common.ExtractSortByParam(r, defaultFBPageSortField, defaultFBPageSortOrder, allowedFBPageSortFields)
	if err != nil {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid sort param: %v", err))
		return
	}
	if !slices.Contains(allowedFBPageSortFields, sortByParam.Field) {
		transhttp.RespondError(w, http.StatusBadRequest, fmt.Sprintf("Invalid sort field, supported fields: %+v", allowedFBPageSortFields))
		return
	}

	grpcListPageReq := &services.ListPagesRequest{
		Page:      int32(paginationParam.Page),
		Limit:     int32(paginationParam.Limit),
		SortField: mapFBPageSortFieldToDBField[sortByParam.Field],
		IsAsc:     sortByParam.Order == "asc",
	}
	resp, err := h.PageClient.ListPages(r.Context(), grpcListPageReq)
	if err != nil {
		atlogger.Errorw("Failed to list pages",
			"error", err,
		)
		transhttp.RespondError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to list pages: %v", err))
		return
	}

	// Prepare response
	pages := make([]fbpage.Page, len(resp.Pages))
	for i, page := range resp.Pages {
		pages[i] = fbpage.Page{
			ID:   page.Id,
			Name: page.Name,
		}
	}
	listPagesResp := &ListPagesResponse{
		Pages: pages,
		Pagination: common.Pagination{
			Page:  paginationParam.Page,
			Limit: paginationParam.Limit,
			Total: resp.Total,
		},
	}
	transhttp.RespondJSON(w, http.StatusOK, listPagesResp)

}
