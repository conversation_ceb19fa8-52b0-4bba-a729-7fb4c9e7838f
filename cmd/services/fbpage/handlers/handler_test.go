package handlers

import (
	"context"
	"database/sql"
	"os"
	"testing"

	"github.com/jmoiron/sqlx"
	"github.com/sqlc-dev/pqtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	fbpage_sqlcgen "gitlab.com/a7923/athena-go/sqlc/fbpage/sqlcgen"

	_ "github.com/lib/pq"
)

func TestRegisterPage(t *testing.T) {
	t.Parallel()

	ctx := context.Background()

	dbURL := os.Getenv("DB_URL")
	db, err := sqlx.ConnectContext(ctx, "postgres", dbURL)
	if err != nil {
		t.Skip("Failed to connect to database: ", err)
	}

	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		t.Skip("Failed to begin transaction: ", err)
	}
	t.Cleanup(func() {
		tx.Rollback()
		db.Close()
	})

	queries := fbpage_sqlcgen.New(tx)
	testCases := []struct {
		testName string
		req      *services.RegisterPageRequest
	}{
		{
			testName: "Register new page",
			req: &services.RegisterPageRequest{
				PageId:          "1234567890",
				PageName:        "Test Page",
				PageAccessToken: "test_access_token",
				AppSecret:       "test_app_secret",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.testName, func(t *testing.T) {
			t.Parallel()
			err := registerPage(ctx, queries, tc.req)
			require.NoError(t, err, "Failed to register page")

			page := &fbpage_sqlcgen.Page{}
			row := tx.QueryRowContext(ctx, "SELECT * FROM pages WHERE id = $1", tc.req.PageId)
			require.NoError(t, row.Err(), "Failed to query page")

			err = row.Scan(&page.ID, &page.Name, &page.AccessToken, &page.AppSecret, &page.CreatedAt, &page.UpdatedAt)
			require.NoError(t, err, "Failed to scan page")

			assert.Equal(t, tc.req.PageName, page.Name)
			assert.Equal(t, tc.req.PageAccessToken, page.AccessToken)
			assert.Equal(t, tc.req.AppSecret, page.AppSecret)
		})
	}
}

func TestBulkDeletePages(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	dbURL := os.Getenv("DB_URL")
	db, err := sqlx.ConnectContext(ctx, "postgres", dbURL)
	if err != nil {
		t.Skip("Failed to connect to database: ", err)
	}

	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		t.Skip("Failed to begin transaction: ", err)
	}
	t.Cleanup(func() {
		tx.Rollback()
		db.Close()
	})

	pageIDs := []string{"1234567890", "0987654321"}
	for _, id := range pageIDs {
		_, err := tx.ExecContext(ctx,
			"INSERT INTO pages (id, name, access_token, app_secret) VALUES ($1, $2, $3, $4)",
			id, "Test Page", "test_access_token", "test_app_secret")
		require.NoError(t, err, "Failed to insert page")
	}

	queries := fbpage_sqlcgen.New(tx)
	testCases := []struct {
		testName string
		req      *services.BulkDeletePagesRequest
		err      error
	}{
		{
			testName: "Delete multiple pages",
			req: &services.BulkDeletePagesRequest{
				PageIds: []string{"1234567890", "0987654321"},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.testName, func(t *testing.T) {
			t.Parallel()
			err := deletePages(ctx, queries, tc.req.PageIds)
			require.NoError(t, err, "Failed to delete pages")

			var page fbpage_sqlcgen.Page
			for _, id := range tc.req.PageIds {
				row := tx.QueryRowContext(ctx, "SELECT * FROM pages WHERE id = $1", id)
				err := row.Scan(&page.ID, &page.Name, &page.AccessToken, &page.AppSecret, &page.CreatedAt, &page.UpdatedAt)
				require.EqualError(t, err, "sql: no rows in result set", "Page should not exist")
			}
		})
	}
}

func TestListPosts(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	dbURL := os.Getenv("DB_URL")
	db, err := sqlx.ConnectContext(ctx, "postgres", dbURL)
	if err != nil {
		t.Skip("Failed to connect to database: ", err)
	}

	t.Cleanup(func() {
		db.Close()
	})

	pageIDs := []string{"1234567890-TWO", "0987654321-EMPTY"}
	for _, id := range pageIDs {
		_, err := db.ExecContext(ctx,
			"INSERT INTO pages (id, name, access_token, app_secret) VALUES ($1, $2, $3, $4)",
			id, "Test Page", "test_access_token", "test_app_secret")
		require.NoError(t, err, "Failed to insert page")
	}

	t.Cleanup(func() {
		for _, id := range pageIDs {
			_, err := db.ExecContext(ctx, "DELETE FROM pages WHERE id = $1", id)
			require.NoError(t, err, "Failed to delete page")
		}
	})

	posts := []fbpage_sqlcgen.Post{
		{
			ID:          "1234567890",
			PageID:      "1234567890-TWO",
			TextContent: sql.NullString{String: "Test post", Valid: true},
			Url:         sql.NullString{String: "https://www.facebook.com/1234567890", Valid: true},
			Attachments: pqtype.NullRawMessage{RawMessage: []byte("{}"), Valid: true},
		},
		{
			ID:          "1234567891",
			PageID:      "1234567890-TWO",
			TextContent: sql.NullString{String: "Test post 2", Valid: true},
			Url:         sql.NullString{String: "https://www.facebook.com/1234567891", Valid: true},
			Attachments: pqtype.NullRawMessage{RawMessage: []byte(`{
				"data": [
					{
						"media_type": "PHOTO",
						"media_url": "https://www.facebook.com/1234567891/media/1"
					}
				]
			}`), Valid: true},
		},
	}

	for _, post := range posts {
		_, err := db.ExecContext(ctx,
			"INSERT INTO posts (id, text_content, url, attachments, page_id) VALUES ($1, $2, $3, $4, $5)",
			post.ID, post.TextContent, post.Url, post.Attachments, post.PageID)
		require.NoError(t, err, "Failed to insert post")
	}

	t.Cleanup(func() {
		for _, post := range posts {
			_, err := db.ExecContext(ctx, "DELETE FROM posts WHERE id = $1", post.ID)
			require.NoError(t, err, "Failed to delete post")
		}
	})

	handler := NewFBPageHandler(db, nil)
	resp := &services.ListPostsResponse{}
	err = handler.listPosts(ctx, &services.ListPostsRequest{
		PageId: "1234567890-TWO",
		Page:   1,
		Limit:  10,
	}, resp)
	require.NoError(t, err, "Failed to list posts")
	require.Equal(t, len(posts), len(resp.Posts), "Incorrect number of posts")

	for _, post := range resp.Posts {
		t.Logf("Post: ID: %s, PageID: %s, TextContent: %s, URL: %s, Attachments: %s\n",
			post.Id, post.PageId, post.TextContent, post.Url, post.Attachments)
	}
}
