package handlers

import (
	"context"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/jmoiron/sqlx"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/messenger"
	"gitlab.com/a7923/athena-go/proto/exmsg/models"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
	fbpage_sqlcgen "gitlab.com/a7923/athena-go/sqlc/fbpage/sqlcgen"
)

var _ services.FBPageServiceHandler = (*FBPageHandler)(nil)

type FBPageHandler struct {
	queries   *fbpage_sqlcgen.Queries
	msgSender messenger.MessageSender
	db        *sqlx.DB
}

func NewFBPageHandler(db *sqlx.DB, msgSender messenger.MessageSender) *FBPageHandler {
	return &FBPageHandler{
		queries:   fbpage_sqlcgen.New(db),
		msgSender: msgSender,
		db:        db,
	}
}

func (h *FBPageHandler) SendMessage(ctx context.Context, req *services.SendMessageRequest, resp *services.SendMessageResponse) error {
	return h.msgSender.Send(ctx, &messenger.SendRequest{
		PageID:        req.PageId,
		RecipientID:   req.RecipientId,
		Message:       convertRPCMessageToOutboundMessage(req.Message),
		MessagingType: req.MessagingType,
	})
}

// convertRPCMessageToOutboundMessage converts a protobuf message to a messenger.OutboundMessage
func convertRPCMessageToOutboundMessage(msg *models.OutboundMessage) messenger.OutboundMessage {
	outMsg := messenger.OutboundMessage{}
	if msg.Text != nil {
		outMsg.Text = *msg.Text
	}
	if msg.Attachment != nil {
		outMsg.Attachment = &messenger.Attachment{
			Type: msg.Attachment.Type,
			Payload: messenger.AttachmentPayload{
				URL: msg.Attachment.Payload.Url,
			},
		}
	}
	return outMsg
}

// RegisterPage insert page's informations to the database. If the page already exists, it will be updated.
func (h *FBPageHandler) RegisterPage(ctx context.Context, req *services.RegisterPageRequest, resp *services.RegisterPageResponse) error {
	err := registerPage(ctx, h.queries, req)
	if err != nil {
		logger.FromCtx(ctx).Errorw("Failed to register page", "error", err, "req", fmt.Sprintf("%+v", req))
		return err
	}
	return nil
}

func registerPage(ctx context.Context, queries *fbpage_sqlcgen.Queries, req *services.RegisterPageRequest) error {
	_, err := queries.CreatePage(ctx, fbpage_sqlcgen.CreatePageParams{
		ID:          req.PageId,
		Name:        req.PageName,
		AccessToken: req.PageAccessToken,
		AppSecret:   req.AppSecret,
	})
	return err
}

// BulkDeletePages delete multiple pages from the database.
func (h *FBPageHandler) BulkDeletePages(ctx context.Context, req *services.BulkDeletePagesRequest, resp *services.BulkDeletePagesResponse) error {
	return deletePages(ctx, h.queries, req.PageIds)
}

func deletePages(ctx context.Context, queries *fbpage_sqlcgen.Queries, pageIDs []string) error {
	return queries.DeletePages(ctx, pageIDs)
}

// ListPages list pages from the database.
func (h *FBPageHandler) ListPages(ctx context.Context, req *services.ListPagesRequest, resp *services.ListPagesResponse) error {
	return h.listPages(ctx, req, resp)
}

func (h *FBPageHandler) listPages(ctx context.Context, req *services.ListPagesRequest, resp *services.ListPagesResponse) error {
	limit, offset := req.Limit, req.Limit*(req.Page-1)

	queryBuilder := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar)
	query := queryBuilder.Select("id", "name").
		From("pages").
		OrderBy("id ASC").
		Offset(uint64(offset)).
		Limit(uint64(limit))

	stmt, args, err := query.ToSql()
	if err != nil {
		return err
	}

	pages := make([]*models.FBPage, 0)
	err = h.db.SelectContext(ctx, &pages, stmt, args...)
	if err != nil {
		return err
	}
	resp.Pages = pages
	return nil
}

// ListPosts list posts of a page from the database (give Page ID)
func (h *FBPageHandler) ListPosts(ctx context.Context, req *services.ListPostsRequest, resp *services.ListPostsResponse) error {
	return h.listPosts(ctx, req, resp)
}

func (h *FBPageHandler) listPosts(ctx context.Context, req *services.ListPostsRequest, resp *services.ListPostsResponse) error {
	limit, offset := req.Limit, req.Limit*(req.Page-1)

	queryBuilder := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar)
	query := queryBuilder.Select("id", "text_content", "url", "attachments").
		From("posts").
		Where("page_id = ?", req.PageId).
		OrderBy("id ASC").
		Offset(uint64(offset)).
		Limit(uint64(limit))

	stmt, args, err := query.ToSql()
	if err != nil {
		return err
	}

	posts := make([]*models.Post, 0)
	rows, err := h.db.QueryContext(ctx, stmt, args...)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		post := models.Post{}
		if err := rows.Scan(
			&post.Id,
			&post.TextContent,
			&post.Url,
			&post.Attachments,
		); err != nil {
			return err
		}
		post.PageId = req.PageId
		posts = append(posts, &post)
	}

	resp.Posts = posts

	return nil
}
