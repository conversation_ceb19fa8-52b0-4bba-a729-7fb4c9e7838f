package main

import (
	"net/http"
	"time"

	"gitlab.com/a7923/athena-go/cmd/services/fbpage/handlers"
	"gitlab.com/a7923/athena-go/cmd/services/fbpage/page"
	"gitlab.com/a7923/athena-go/pkg/app"
	"gitlab.com/a7923/athena-go/pkg/common"
	"gitlab.com/a7923/athena-go/pkg/dbtool"
	"gitlab.com/a7923/athena-go/pkg/logger"
	"gitlab.com/a7923/athena-go/pkg/messenger"
	"gitlab.com/a7923/athena-go/proto/exmsg/services"
)

func main() {
	svc := app.NewGRPCService(common.ServiceNameFBPage)

	// Create database connection
	psql, err := dbtool.NewConnectionManager(dbtool.DBTypePostgreSQL, nil)
	if err != nil {
		logger.AthenaLogger.Fatal("Failed to connect to database: ", err)
	}
	db := psql.GetConnection()

	// Initialize message sender
	httpClient := &http.Client{
		Timeout: 90 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 100,
			IdleConnTimeout:     90 * time.Second,
		},
	}
	msgSender := messenger.NewHTTPSender(httpClient, page.NewPostgresPageTokenProvider(db))

	// Create gRPC service
	grpcSvc := handlers.NewFBPageHandler(db, msgSender)
	err = services.RegisterFBPageServiceHandler(svc.Server(), grpcSvc)
	if err != nil {
		logger.AthenaLogger.Fatal(err)
	}

	err = svc.Run()
	if err != nil {
		logger.AthenaLogger.Fatal(err)
	}
}
