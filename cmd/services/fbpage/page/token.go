package page

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.com/a7923/athena-go/pkg/messenger"
	fbpage_sqlcgen "gitlab.com/a7923/athena-go/sqlc/fbpage/sqlcgen"
)

var _ messenger.PageTokenProvider = (*postgresPageTokenProvider)(nil)

type postgresPageTokenProvider struct {
	queries *fbpage_sqlcgen.Queries
}

func NewPostgresPageTokenProvider(db *sqlx.DB) messenger.PageTokenProvider {
	return &postgresPageTokenProvider{
		queries: fbpage_sqlcgen.New(db),
	}
}

func (p *postgresPageTokenProvider) GetAccessToken(ctx context.Context, pageID string) (string, error) {
	return p.queries.GetAccessToken(ctx, pageID)
}
