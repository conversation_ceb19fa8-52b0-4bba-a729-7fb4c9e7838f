package page

import (
	"context"
	"os"
	"testing"

	"github.com/jmoiron/sqlx"
	fbpage_sqlcgen "gitlab.com/a7923/athena-go/sqlc/fbpage/sqlcgen"
)

func TestGetAccessToken(t *testing.T) {
	t.<PERSON>llel()

	ctx := context.Background()

	dbURL := os.Getenv("DB_URL")
	db, err := sqlx.ConnectContext(ctx, "postgres", dbURL)
	if err != nil {
		t.Skip("Failed to connect to database: ", err)
	}

	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		t.Skip("Failed to begin transaction: ", err)
	}
	t.Cleanup(func() {
		tx.Rollback()
		db.Close()
	})

	pageID := "1234567890"
	token := "test_access_token"

	_, err = tx.ExecContext(ctx,
		"INSERT INTO pages (id, name, access_token, app_secret) VALUES ($1, $2, $3, $4)",
		pageID, "Test Page", token, "test_app_secret")

	if err != nil {
		t.Skip("Failed to insert page: ", err)
	}

	queries := fbpage_sqlcgen.New(tx)
	testCases := []struct {
		testName string
		pageID   string
		token    string
	}{
		{
			testName: "Get access token",
			pageID:   pageID,
			token:    token,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.testName, func(t *testing.T) {
			t.Parallel()
			got, err := queries.GetAccessToken(ctx, tc.pageID)
			if err != nil {
				t.Errorf("Failed to get access token: %v", err)
			}
			if got != tc.token {
				t.Errorf("Expected access token: %s, got: %s", tc.token, got)
			}
		})
	}
}
