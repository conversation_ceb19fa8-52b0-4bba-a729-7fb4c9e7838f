package fbpage

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/jmoiron/sqlx"
)

// bootstrapData fetch pages from staging database and insert into local database
func bootstrapData(ctx context.Context, stagingDB *sqlx.DB) error {
	tokens, err := ListActiveTokens(ctx, stagingDB)
	if err != nil {
		return err
	}

	httpClient := &http.Client{
		Timeout: 90 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 100,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	expiredTokens := make([]string, 0)
	mu := sync.Mutex{}

	tokenCh := make(chan string, 1000)
	wg := sync.WaitGroup{}

	for _, token := range tokens {
		wg.Add(1)
		go func() {
			tokenCh <- token
		}()
	}

	workerContext, cancel := context.WithCancel(ctx)
	defer cancel()

	workers := 100
	for range workers {
		go func(ctx context.Context, ch chan string) {
			for {
				select {
				case token := <-ch:
					valid, err := queryValid(ctx, httpClient, token)
					if err != nil {
						log.Printf("Failed to query valid token %s: %v", token, err)
					}
					if !valid {
						mu.Lock()
						expiredTokens = append(expiredTokens, token)
						mu.Unlock()
					}
					wg.Done()
				case <-ctx.Done():
					return
				}
			}
		}(workerContext, tokenCh)
	}
	wg.Wait()

	return nil
}

const (
	DebugTokenEndpoint = "https://graph.facebook.com/v22.0/me?access_token=%s"
)

// queryValid receives token and check if it still valid
func queryValid(ctx context.Context, client *http.Client, token string) (bool, error) {
	debugURL := fmt.Sprintf(DebugTokenEndpoint, token)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, debugURL, nil)
	if err != nil {
		return false, err
	}

	resp, err := client.Do(req)
	if err != nil {
		return false, err
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK, nil
}

const (
	ListActiveTokensQuery = `
		SELECT access_token
		FROM scoped_user_pages
		WHERE expired = false AND access_token != '' AND access_token IS NOT NULL
	`

	UpdateExpiredTokensQuery = `
		UPDATE scoped_user_pages
		SET expired = true
		WHERE access_token IN (?)
	`
)

// ListActiveTokens lists all active tokens (record that has field 'expired' = false) from table 'scoped_user_pages'
func ListActiveTokens(ctx context.Context, db *sqlx.DB) ([]string, error) {
	var tokens []string
	err := db.SelectContext(ctx, &tokens, ListActiveTokensQuery)
	if err != nil {
		return nil, err
	}
	return tokens, nil
}

// UpdateExpiredTokens updates all tokens to expired (set field 'expired' = true) in table 'scoped_user_pages'
func UpdateExpiredTokens(ctx context.Context, db *sqlx.DB, tokens []string) error {
	query, args, err := sqlx.In(UpdateExpiredTokensQuery, tokens)
	if err != nil {
		return err
	}

	query = db.Rebind(query)
	_, err = db.ExecContext(ctx, query, args...)
	if err != nil {
		return err
	}

	return nil
}
