package messenger

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"gitlab.com/a7923/athena-go/pkg/logger"
)

type OutboundMessage struct {
	Text       string      `json:"text,omitempty"`
	Attachment *Attachment `json:"attachment,omitempty"`
}

type SendRequest struct {
	PageID        string
	RecipientID   string
	Message       OutboundMessage
	MessagingType string
}

// MessageSender sends message to Facebook Messenger.
type MessageSender interface {
	Send(ctx context.Context, req *SendRequest) error
}

// httpSender is an implementation of MessageSender that sends message using HTTP.
type httpSender struct {
	client        *http.Client
	tokenProvider PageTokenProvider
	apiBaseURL    string
}

func NewHTTPSender(client *http.Client, provider PageTokenProvider) MessageSender {
	return &httpSender{
		client:        client,
		tokenProvider: provider,
		apiBaseURL:    "https://graph.facebook.com/v22.0",
	}
}

type facebookRequestBody struct {
	Recipient     map[string]string `json:"recipient"`
	Message       OutboundMessage   `json:"message"`
	MessagingType string            `json:"messaging_type"`
}

func (s *httpSender) Send(ctx context.Context, req *SendRequest) error {
	accessToken, err := s.tokenProvider.GetAccessToken(ctx, req.PageID)
	if err != nil {
		return fmt.Errorf("could not get access token for page %s: %w", req.PageID, err)
	}

	fbReqBody := facebookRequestBody{
		Recipient:     map[string]string{"id": req.RecipientID},
		Message:       req.Message,
		MessagingType: req.MessagingType,
	}
	bodyBytes, err := json.Marshal(fbReqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	apiURL := fmt.Sprintf("%s/me/messages?access_token=%s", s.apiBaseURL, accessToken)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", apiURL, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return fmt.Errorf("failed to create http request: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := s.client.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to send request to Facebook API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		respBody, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("facebook API returned non-200 status [%d]: %s", resp.StatusCode, string(respBody))
	}

	logger.AthenaLogger.Infow("Successfully sent message", "page", req.PageID, "recipient", req.RecipientID)

	return nil
}
