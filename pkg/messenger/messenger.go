package messenger

// WebhookEvent represents the payload sent by Facebook Messenger when a webhook is triggered.
type WebhookEvent struct {
	Object string  `json:"object"`
	Entry  []Entry `json:"entry"`
}

// Entry represents a single entry in the webhook payload.
type Entry struct {
	ID        string      `json:"id"`
	Time      int64       `json:"time"`
	Messaging []Messaging `json:"messaging"`
}

// Messaging represents a single messaging event in the webhook payload.
type Messaging struct {
	Sender     Sender      `json:"sender"`
	Recipient  Recipient   `json:"recipient"`
	Timestamp  int64       `json:"timestamp"`
	Message    *Message    `json:"message,omitempty"`
	Postback   *Postback   `json:"postback,omitempty"`
	Attachment *Attachment `json:"attachment,omitempty"`
}

// Sender represents the sender of a messaging event.
type Sender struct {
	ID string `json:"id"`
}

// Recipient represents the recipient of a messaging event.
type Recipient struct {
	ID string `json:"id"`
}

// Message represents a message sent in a messaging event.
type Message struct {
	MID  string `json:"mid"`
	Text string `json:"text"`
}

// Postback represents a postback event.
type Postback struct {
	Title   string `json:"title"`
	Payload string `json:"payload"`
}

// Attachment represents an attachment sent in a messaging event.
type Attachment struct {
	Type    string            `json:"type"`
	Payload AttachmentPayload `json:"payload"`
}

// AttachmentPayload represents the payload of an attachment.
type AttachmentPayload struct {
	URL string `json:"url"`
}
