package messenger

import (
	"context"
	"net/http"
	"os"
	"testing"
)

// staticTokenProvider implements PageTokenProvider and always returns a static token (for testing purposes)
type staticTokenProvider struct {
	token string
}

// GetAccessToken always returns the static token
func (p staticTokenProvider) GetAccessToken(ctx context.Context, pageID string) (string, error) {
	return p.token, nil
}

func TestHTTPSend(t *testing.T) {
	t.Parallel()

	var (
		pageAccessToken = os.Getenv("TEST_TOKEN")
		recipientID     = os.Getenv("TEST_RECIPIENT_ID")
		pageID          = os.Getenv("TEST_PAGE_ID")
	)

	if pageAccessToken == "" || recipientID == "" || pageID == "" {
		t.Skip("Skipping integration test: TEST_TOKEN, TEST_RECIPIENT_ID, and TEST_PAGE_ID environment variables must be set")
	}

	client := http.DefaultClient
	provider := staticTokenProvider{token: pageAccessToken}
	sender := NewHTTPSender(client, provider)

	testCases := []struct {
		name string
		req  *SendRequest
	}{
		{
			name: "Send text message",
			req: &SendRequest{
				PageID:        pageID,
				RecipientID:   recipientID,
				Message:       OutboundMessage{Text: "Hello world"},
				MessagingType: "RESPONSE",
			},
		},
		{
			name: "Send attachment",
			req: &SendRequest{
				PageID:        pageID,
				RecipientID:   recipientID,
				Message:       OutboundMessage{Attachment: &Attachment{Type: "image", Payload: AttachmentPayload{URL: "https://www.bigfootdigital.co.uk/wp-content/uploads/2020/07/image-optimisation-scaled.jpg"}}},
				MessagingType: "RESPONSE",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			err := sender.Send(context.Background(), tc.req)
			if err != nil {
				t.Errorf("failed to send message: %v", err)
			}
		})
	}

}
