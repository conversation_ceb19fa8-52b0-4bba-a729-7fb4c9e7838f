// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: services/fbpage.proto

package services

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FBPageService_SendMessage_FullMethodName     = "/exmsg.services.FBPageService/SendMessage"
	FBPageService_RegisterPage_FullMethodName    = "/exmsg.services.FBPageService/RegisterPage"
	FBPageService_BulkDeletePages_FullMethodName = "/exmsg.services.FBPageService/BulkDeletePages"
	FBPageService_ListPages_FullMethodName       = "/exmsg.services.FBPageService/ListPages"
	FBPageService_ListPosts_FullMethodName       = "/exmsg.services.FBPageService/ListPosts"
)

// FBPageServiceClient is the client API for FBPageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FBPageServiceClient interface {
	SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResponse, error)
	RegisterPage(ctx context.Context, in *RegisterPageRequest, opts ...grpc.CallOption) (*RegisterPageResponse, error)
	BulkDeletePages(ctx context.Context, in *BulkDeletePagesRequest, opts ...grpc.CallOption) (*BulkDeletePagesResponse, error)
	ListPages(ctx context.Context, in *ListPagesRequest, opts ...grpc.CallOption) (*ListPagesResponse, error)
	ListPosts(ctx context.Context, in *ListPostsRequest, opts ...grpc.CallOption) (*ListPostsResponse, error)
}

type fBPageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFBPageServiceClient(cc grpc.ClientConnInterface) FBPageServiceClient {
	return &fBPageServiceClient{cc}
}

func (c *fBPageServiceClient) SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendMessageResponse)
	err := c.cc.Invoke(ctx, FBPageService_SendMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fBPageServiceClient) RegisterPage(ctx context.Context, in *RegisterPageRequest, opts ...grpc.CallOption) (*RegisterPageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterPageResponse)
	err := c.cc.Invoke(ctx, FBPageService_RegisterPage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fBPageServiceClient) BulkDeletePages(ctx context.Context, in *BulkDeletePagesRequest, opts ...grpc.CallOption) (*BulkDeletePagesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BulkDeletePagesResponse)
	err := c.cc.Invoke(ctx, FBPageService_BulkDeletePages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fBPageServiceClient) ListPages(ctx context.Context, in *ListPagesRequest, opts ...grpc.CallOption) (*ListPagesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListPagesResponse)
	err := c.cc.Invoke(ctx, FBPageService_ListPages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fBPageServiceClient) ListPosts(ctx context.Context, in *ListPostsRequest, opts ...grpc.CallOption) (*ListPostsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListPostsResponse)
	err := c.cc.Invoke(ctx, FBPageService_ListPosts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FBPageServiceServer is the server API for FBPageService service.
// All implementations must embed UnimplementedFBPageServiceServer
// for forward compatibility.
type FBPageServiceServer interface {
	SendMessage(context.Context, *SendMessageRequest) (*SendMessageResponse, error)
	RegisterPage(context.Context, *RegisterPageRequest) (*RegisterPageResponse, error)
	BulkDeletePages(context.Context, *BulkDeletePagesRequest) (*BulkDeletePagesResponse, error)
	ListPages(context.Context, *ListPagesRequest) (*ListPagesResponse, error)
	ListPosts(context.Context, *ListPostsRequest) (*ListPostsResponse, error)
	mustEmbedUnimplementedFBPageServiceServer()
}

// UnimplementedFBPageServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFBPageServiceServer struct{}

func (UnimplementedFBPageServiceServer) SendMessage(context.Context, *SendMessageRequest) (*SendMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessage not implemented")
}
func (UnimplementedFBPageServiceServer) RegisterPage(context.Context, *RegisterPageRequest) (*RegisterPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterPage not implemented")
}
func (UnimplementedFBPageServiceServer) BulkDeletePages(context.Context, *BulkDeletePagesRequest) (*BulkDeletePagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulkDeletePages not implemented")
}
func (UnimplementedFBPageServiceServer) ListPages(context.Context, *ListPagesRequest) (*ListPagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPages not implemented")
}
func (UnimplementedFBPageServiceServer) ListPosts(context.Context, *ListPostsRequest) (*ListPostsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPosts not implemented")
}
func (UnimplementedFBPageServiceServer) mustEmbedUnimplementedFBPageServiceServer() {}
func (UnimplementedFBPageServiceServer) testEmbeddedByValue()                       {}

// UnsafeFBPageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FBPageServiceServer will
// result in compilation errors.
type UnsafeFBPageServiceServer interface {
	mustEmbedUnimplementedFBPageServiceServer()
}

func RegisterFBPageServiceServer(s grpc.ServiceRegistrar, srv FBPageServiceServer) {
	// If the following call pancis, it indicates UnimplementedFBPageServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FBPageService_ServiceDesc, srv)
}

func _FBPageService_SendMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FBPageServiceServer).SendMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FBPageService_SendMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FBPageServiceServer).SendMessage(ctx, req.(*SendMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FBPageService_RegisterPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FBPageServiceServer).RegisterPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FBPageService_RegisterPage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FBPageServiceServer).RegisterPage(ctx, req.(*RegisterPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FBPageService_BulkDeletePages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkDeletePagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FBPageServiceServer).BulkDeletePages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FBPageService_BulkDeletePages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FBPageServiceServer).BulkDeletePages(ctx, req.(*BulkDeletePagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FBPageService_ListPages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FBPageServiceServer).ListPages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FBPageService_ListPages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FBPageServiceServer).ListPages(ctx, req.(*ListPagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FBPageService_ListPosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPostsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FBPageServiceServer).ListPosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FBPageService_ListPosts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FBPageServiceServer).ListPosts(ctx, req.(*ListPostsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FBPageService_ServiceDesc is the grpc.ServiceDesc for FBPageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FBPageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "exmsg.services.FBPageService",
	HandlerType: (*FBPageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendMessage",
			Handler:    _FBPageService_SendMessage_Handler,
		},
		{
			MethodName: "RegisterPage",
			Handler:    _FBPageService_RegisterPage_Handler,
		},
		{
			MethodName: "BulkDeletePages",
			Handler:    _FBPageService_BulkDeletePages_Handler,
		},
		{
			MethodName: "ListPages",
			Handler:    _FBPageService_ListPages_Handler,
		},
		{
			MethodName: "ListPosts",
			Handler:    _FBPageService_ListPosts_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/fbpage.proto",
}
