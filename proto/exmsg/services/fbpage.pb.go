// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: services/fbpage.proto

package services

import (
	models "gitlab.com/a7923/athena-go/proto/exmsg/models"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SendMessageRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	PageId        string                  `protobuf:"bytes,1,opt,name=page_id,json=pageId,proto3" json:"page_id,omitempty"`
	RecipientId   string                  `protobuf:"bytes,2,opt,name=recipient_id,json=recipientId,proto3" json:"recipient_id,omitempty"`
	Message       *models.OutboundMessage `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	MessagingType string                  `protobuf:"bytes,4,opt,name=messaging_type,json=messagingType,proto3" json:"messaging_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageRequest) Reset() {
	*x = SendMessageRequest{}
	mi := &file_services_fbpage_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageRequest) ProtoMessage() {}

func (x *SendMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_fbpage_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageRequest.ProtoReflect.Descriptor instead.
func (*SendMessageRequest) Descriptor() ([]byte, []int) {
	return file_services_fbpage_proto_rawDescGZIP(), []int{0}
}

func (x *SendMessageRequest) GetPageId() string {
	if x != nil {
		return x.PageId
	}
	return ""
}

func (x *SendMessageRequest) GetRecipientId() string {
	if x != nil {
		return x.RecipientId
	}
	return ""
}

func (x *SendMessageRequest) GetMessage() *models.OutboundMessage {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *SendMessageRequest) GetMessagingType() string {
	if x != nil {
		return x.MessagingType
	}
	return ""
}

type SendMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RecipientId   string                 `protobuf:"bytes,1,opt,name=recipient_id,json=recipientId,proto3" json:"recipient_id,omitempty"`
	MessageId     string                 `protobuf:"bytes,2,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageResponse) Reset() {
	*x = SendMessageResponse{}
	mi := &file_services_fbpage_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageResponse) ProtoMessage() {}

func (x *SendMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_fbpage_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageResponse.ProtoReflect.Descriptor instead.
func (*SendMessageResponse) Descriptor() ([]byte, []int) {
	return file_services_fbpage_proto_rawDescGZIP(), []int{1}
}

func (x *SendMessageResponse) GetRecipientId() string {
	if x != nil {
		return x.RecipientId
	}
	return ""
}

func (x *SendMessageResponse) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

type RegisterPageRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PageId          string                 `protobuf:"bytes,1,opt,name=page_id,json=pageId,proto3" json:"page_id,omitempty"`
	PageName        string                 `protobuf:"bytes,2,opt,name=page_name,json=pageName,proto3" json:"page_name,omitempty"`
	PageAccessToken string                 `protobuf:"bytes,3,opt,name=page_access_token,json=pageAccessToken,proto3" json:"page_access_token,omitempty"`
	AppSecret       string                 `protobuf:"bytes,4,opt,name=app_secret,json=appSecret,proto3" json:"app_secret,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *RegisterPageRequest) Reset() {
	*x = RegisterPageRequest{}
	mi := &file_services_fbpage_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterPageRequest) ProtoMessage() {}

func (x *RegisterPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_fbpage_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterPageRequest.ProtoReflect.Descriptor instead.
func (*RegisterPageRequest) Descriptor() ([]byte, []int) {
	return file_services_fbpage_proto_rawDescGZIP(), []int{2}
}

func (x *RegisterPageRequest) GetPageId() string {
	if x != nil {
		return x.PageId
	}
	return ""
}

func (x *RegisterPageRequest) GetPageName() string {
	if x != nil {
		return x.PageName
	}
	return ""
}

func (x *RegisterPageRequest) GetPageAccessToken() string {
	if x != nil {
		return x.PageAccessToken
	}
	return ""
}

func (x *RegisterPageRequest) GetAppSecret() string {
	if x != nil {
		return x.AppSecret
	}
	return ""
}

type RegisterPageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterPageResponse) Reset() {
	*x = RegisterPageResponse{}
	mi := &file_services_fbpage_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterPageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterPageResponse) ProtoMessage() {}

func (x *RegisterPageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_fbpage_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterPageResponse.ProtoReflect.Descriptor instead.
func (*RegisterPageResponse) Descriptor() ([]byte, []int) {
	return file_services_fbpage_proto_rawDescGZIP(), []int{3}
}

func (x *RegisterPageResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type BulkDeletePagesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageIds       []string               `protobuf:"bytes,1,rep,name=page_ids,json=pageIds,proto3" json:"page_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkDeletePagesRequest) Reset() {
	*x = BulkDeletePagesRequest{}
	mi := &file_services_fbpage_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkDeletePagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkDeletePagesRequest) ProtoMessage() {}

func (x *BulkDeletePagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_fbpage_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkDeletePagesRequest.ProtoReflect.Descriptor instead.
func (*BulkDeletePagesRequest) Descriptor() ([]byte, []int) {
	return file_services_fbpage_proto_rawDescGZIP(), []int{4}
}

func (x *BulkDeletePagesRequest) GetPageIds() []string {
	if x != nil {
		return x.PageIds
	}
	return nil
}

type BulkDeletePagesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BulkDeletePagesResponse) Reset() {
	*x = BulkDeletePagesResponse{}
	mi := &file_services_fbpage_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BulkDeletePagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkDeletePagesResponse) ProtoMessage() {}

func (x *BulkDeletePagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_fbpage_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkDeletePagesResponse.ProtoReflect.Descriptor instead.
func (*BulkDeletePagesResponse) Descriptor() ([]byte, []int) {
	return file_services_fbpage_proto_rawDescGZIP(), []int{5}
}

type ListPagesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	SortField     string                 `protobuf:"bytes,3,opt,name=sort_field,json=sortField,proto3" json:"sort_field,omitempty"`
	IsAsc         bool                   `protobuf:"varint,4,opt,name=is_asc,json=isAsc,proto3" json:"is_asc,omitempty"`
	SearchTerm    *string                `protobuf:"bytes,5,opt,name=search_term,json=searchTerm,proto3,oneof" json:"search_term,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPagesRequest) Reset() {
	*x = ListPagesRequest{}
	mi := &file_services_fbpage_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPagesRequest) ProtoMessage() {}

func (x *ListPagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_fbpage_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPagesRequest.ProtoReflect.Descriptor instead.
func (*ListPagesRequest) Descriptor() ([]byte, []int) {
	return file_services_fbpage_proto_rawDescGZIP(), []int{6}
}

func (x *ListPagesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPagesRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListPagesRequest) GetSortField() string {
	if x != nil {
		return x.SortField
	}
	return ""
}

func (x *ListPagesRequest) GetIsAsc() bool {
	if x != nil {
		return x.IsAsc
	}
	return false
}

func (x *ListPagesRequest) GetSearchTerm() string {
	if x != nil && x.SearchTerm != nil {
		return *x.SearchTerm
	}
	return ""
}

type ListPagesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pages         []*models.FBPage       `protobuf:"bytes,1,rep,name=pages,proto3" json:"pages,omitempty"`
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPagesResponse) Reset() {
	*x = ListPagesResponse{}
	mi := &file_services_fbpage_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPagesResponse) ProtoMessage() {}

func (x *ListPagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_fbpage_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPagesResponse.ProtoReflect.Descriptor instead.
func (*ListPagesResponse) Descriptor() ([]byte, []int) {
	return file_services_fbpage_proto_rawDescGZIP(), []int{7}
}

func (x *ListPagesResponse) GetPages() []*models.FBPage {
	if x != nil {
		return x.Pages
	}
	return nil
}

func (x *ListPagesResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ListPostsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	SortField     string                 `protobuf:"bytes,3,opt,name=sort_field,json=sortField,proto3" json:"sort_field,omitempty"`
	IsAsc         bool                   `protobuf:"varint,4,opt,name=is_asc,json=isAsc,proto3" json:"is_asc,omitempty"`
	PageId        string                 `protobuf:"bytes,5,opt,name=page_id,json=pageId,proto3" json:"page_id,omitempty"`
	SearchTerm    *string                `protobuf:"bytes,6,opt,name=search_term,json=searchTerm,proto3,oneof" json:"search_term,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPostsRequest) Reset() {
	*x = ListPostsRequest{}
	mi := &file_services_fbpage_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPostsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPostsRequest) ProtoMessage() {}

func (x *ListPostsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_fbpage_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPostsRequest.ProtoReflect.Descriptor instead.
func (*ListPostsRequest) Descriptor() ([]byte, []int) {
	return file_services_fbpage_proto_rawDescGZIP(), []int{8}
}

func (x *ListPostsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPostsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListPostsRequest) GetSortField() string {
	if x != nil {
		return x.SortField
	}
	return ""
}

func (x *ListPostsRequest) GetIsAsc() bool {
	if x != nil {
		return x.IsAsc
	}
	return false
}

func (x *ListPostsRequest) GetPageId() string {
	if x != nil {
		return x.PageId
	}
	return ""
}

func (x *ListPostsRequest) GetSearchTerm() string {
	if x != nil && x.SearchTerm != nil {
		return *x.SearchTerm
	}
	return ""
}

type ListPostsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Posts         []*models.Post         `protobuf:"bytes,1,rep,name=posts,proto3" json:"posts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPostsResponse) Reset() {
	*x = ListPostsResponse{}
	mi := &file_services_fbpage_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPostsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPostsResponse) ProtoMessage() {}

func (x *ListPostsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_fbpage_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPostsResponse.ProtoReflect.Descriptor instead.
func (*ListPostsResponse) Descriptor() ([]byte, []int) {
	return file_services_fbpage_proto_rawDescGZIP(), []int{9}
}

func (x *ListPostsResponse) GetPosts() []*models.Post {
	if x != nil {
		return x.Posts
	}
	return nil
}

var File_services_fbpage_proto protoreflect.FileDescriptor

var file_services_fbpage_proto_rawDesc = string([]byte{
	0x0a, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x66, 0x62, 0x70, 0x61, 0x67,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x16, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x66, 0x62, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x62, 0x70, 0x61, 0x67, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x62,
	0x70, 0x6f, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb0, 0x01, 0x0a, 0x12, 0x53,
	0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65,
	0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x37, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x4f, 0x75,
	0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0x57, 0x0a,
	0x13, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x69,
	0x70, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x96, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x70, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x70, 0x61, 0x67, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22,
	0x51, 0x0a, 0x14, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0x33, 0x0a, 0x16, 0x42, 0x75, 0x6c, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x42, 0x75, 0x6c, 0x6b, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0xa8, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x69, 0x73, 0x41, 0x73, 0x63, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x22, 0x55, 0x0a,
	0x11, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x70, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x46, 0x42, 0x50, 0x61, 0x67, 0x65, 0x52, 0x05, 0x70, 0x61, 0x67, 0x65, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x22, 0xc1, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x73,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x73, 0x63, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x65, 0x72,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x54, 0x65, 0x72, 0x6d, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x22, 0x3d, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a,
	0x05, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x50, 0x6f, 0x73, 0x74,
	0x52, 0x05, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x32, 0xca, 0x03, 0x0a, 0x0d, 0x46, 0x42, 0x50, 0x61,
	0x67, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x56, 0x0a, 0x0b, 0x53, 0x65, 0x6e,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x22, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x65,
	0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x59, 0x0a, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x50, 0x61, 0x67,
	0x65, 0x12, 0x23, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x0f,
	0x42, 0x75, 0x6c, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12,
	0x26, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x50, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12, 0x20, 0x2e,
	0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x50, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x12,
	0x20, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x21, 0x2e, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x3a, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x61, 0x37, 0x39, 0x32, 0x33, 0x2f, 0x61, 0x74, 0x68, 0x65, 0x6e, 0x61, 0x2d,
	0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x78, 0x6d, 0x73, 0x67, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x3b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_fbpage_proto_rawDescOnce sync.Once
	file_services_fbpage_proto_rawDescData []byte
)

func file_services_fbpage_proto_rawDescGZIP() []byte {
	file_services_fbpage_proto_rawDescOnce.Do(func() {
		file_services_fbpage_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_fbpage_proto_rawDesc), len(file_services_fbpage_proto_rawDesc)))
	})
	return file_services_fbpage_proto_rawDescData
}

var file_services_fbpage_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_services_fbpage_proto_goTypes = []any{
	(*SendMessageRequest)(nil),      // 0: exmsg.services.SendMessageRequest
	(*SendMessageResponse)(nil),     // 1: exmsg.services.SendMessageResponse
	(*RegisterPageRequest)(nil),     // 2: exmsg.services.RegisterPageRequest
	(*RegisterPageResponse)(nil),    // 3: exmsg.services.RegisterPageResponse
	(*BulkDeletePagesRequest)(nil),  // 4: exmsg.services.BulkDeletePagesRequest
	(*BulkDeletePagesResponse)(nil), // 5: exmsg.services.BulkDeletePagesResponse
	(*ListPagesRequest)(nil),        // 6: exmsg.services.ListPagesRequest
	(*ListPagesResponse)(nil),       // 7: exmsg.services.ListPagesResponse
	(*ListPostsRequest)(nil),        // 8: exmsg.services.ListPostsRequest
	(*ListPostsResponse)(nil),       // 9: exmsg.services.ListPostsResponse
	(*models.OutboundMessage)(nil),  // 10: exmsg.models.OutboundMessage
	(*timestamppb.Timestamp)(nil),   // 11: google.protobuf.Timestamp
	(*models.FBPage)(nil),           // 12: exmsg.models.FBPage
	(*models.Post)(nil),             // 13: exmsg.models.Post
}
var file_services_fbpage_proto_depIdxs = []int32{
	10, // 0: exmsg.services.SendMessageRequest.message:type_name -> exmsg.models.OutboundMessage
	11, // 1: exmsg.services.RegisterPageResponse.created_at:type_name -> google.protobuf.Timestamp
	12, // 2: exmsg.services.ListPagesResponse.pages:type_name -> exmsg.models.FBPage
	13, // 3: exmsg.services.ListPostsResponse.posts:type_name -> exmsg.models.Post
	0,  // 4: exmsg.services.FBPageService.SendMessage:input_type -> exmsg.services.SendMessageRequest
	2,  // 5: exmsg.services.FBPageService.RegisterPage:input_type -> exmsg.services.RegisterPageRequest
	4,  // 6: exmsg.services.FBPageService.BulkDeletePages:input_type -> exmsg.services.BulkDeletePagesRequest
	6,  // 7: exmsg.services.FBPageService.ListPages:input_type -> exmsg.services.ListPagesRequest
	8,  // 8: exmsg.services.FBPageService.ListPosts:input_type -> exmsg.services.ListPostsRequest
	1,  // 9: exmsg.services.FBPageService.SendMessage:output_type -> exmsg.services.SendMessageResponse
	3,  // 10: exmsg.services.FBPageService.RegisterPage:output_type -> exmsg.services.RegisterPageResponse
	5,  // 11: exmsg.services.FBPageService.BulkDeletePages:output_type -> exmsg.services.BulkDeletePagesResponse
	7,  // 12: exmsg.services.FBPageService.ListPages:output_type -> exmsg.services.ListPagesResponse
	9,  // 13: exmsg.services.FBPageService.ListPosts:output_type -> exmsg.services.ListPostsResponse
	9,  // [9:14] is the sub-list for method output_type
	4,  // [4:9] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_services_fbpage_proto_init() }
func file_services_fbpage_proto_init() {
	if File_services_fbpage_proto != nil {
		return
	}
	file_services_fbpage_proto_msgTypes[6].OneofWrappers = []any{}
	file_services_fbpage_proto_msgTypes[8].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_fbpage_proto_rawDesc), len(file_services_fbpage_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_fbpage_proto_goTypes,
		DependencyIndexes: file_services_fbpage_proto_depIdxs,
		MessageInfos:      file_services_fbpage_proto_msgTypes,
	}.Build()
	File_services_fbpage_proto = out.File
	file_services_fbpage_proto_goTypes = nil
	file_services_fbpage_proto_depIdxs = nil
}
