// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: services/fbpage.proto

package services

import (
	fmt "fmt"
	_ "gitlab.com/a7923/athena-go/proto/exmsg/models"
	proto "google.golang.org/protobuf/proto"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	math "math"
)

import (
	context "context"
	client "go-micro.dev/v5/client"
	server "go-micro.dev/v5/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// Client API for FBPageService service

type FBPageService interface {
	SendMessage(ctx context.Context, in *SendMessageRequest, opts ...client.CallOption) (*SendMessageResponse, error)
	RegisterPage(ctx context.Context, in *RegisterPageRequest, opts ...client.CallOption) (*RegisterPageResponse, error)
	BulkDeletePages(ctx context.Context, in *BulkDeletePagesRequest, opts ...client.CallOption) (*BulkDeletePagesResponse, error)
	ListPages(ctx context.Context, in *ListPagesRequest, opts ...client.CallOption) (*ListPagesResponse, error)
	ListPosts(ctx context.Context, in *ListPostsRequest, opts ...client.CallOption) (*ListPostsResponse, error)
}

type fBPageService struct {
	c    client.Client
	name string
}

func NewFBPageService(name string, c client.Client) FBPageService {
	return &fBPageService{
		c:    c,
		name: name,
	}
}

func (c *fBPageService) SendMessage(ctx context.Context, in *SendMessageRequest, opts ...client.CallOption) (*SendMessageResponse, error) {
	req := c.c.NewRequest(c.name, "FBPageService.SendMessage", in)
	out := new(SendMessageResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fBPageService) RegisterPage(ctx context.Context, in *RegisterPageRequest, opts ...client.CallOption) (*RegisterPageResponse, error) {
	req := c.c.NewRequest(c.name, "FBPageService.RegisterPage", in)
	out := new(RegisterPageResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fBPageService) BulkDeletePages(ctx context.Context, in *BulkDeletePagesRequest, opts ...client.CallOption) (*BulkDeletePagesResponse, error) {
	req := c.c.NewRequest(c.name, "FBPageService.BulkDeletePages", in)
	out := new(BulkDeletePagesResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fBPageService) ListPages(ctx context.Context, in *ListPagesRequest, opts ...client.CallOption) (*ListPagesResponse, error) {
	req := c.c.NewRequest(c.name, "FBPageService.ListPages", in)
	out := new(ListPagesResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fBPageService) ListPosts(ctx context.Context, in *ListPostsRequest, opts ...client.CallOption) (*ListPostsResponse, error) {
	req := c.c.NewRequest(c.name, "FBPageService.ListPosts", in)
	out := new(ListPostsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for FBPageService service

type FBPageServiceHandler interface {
	SendMessage(context.Context, *SendMessageRequest, *SendMessageResponse) error
	RegisterPage(context.Context, *RegisterPageRequest, *RegisterPageResponse) error
	BulkDeletePages(context.Context, *BulkDeletePagesRequest, *BulkDeletePagesResponse) error
	ListPages(context.Context, *ListPagesRequest, *ListPagesResponse) error
	ListPosts(context.Context, *ListPostsRequest, *ListPostsResponse) error
}

func RegisterFBPageServiceHandler(s server.Server, hdlr FBPageServiceHandler, opts ...server.HandlerOption) error {
	type fBPageService interface {
		SendMessage(ctx context.Context, in *SendMessageRequest, out *SendMessageResponse) error
		RegisterPage(ctx context.Context, in *RegisterPageRequest, out *RegisterPageResponse) error
		BulkDeletePages(ctx context.Context, in *BulkDeletePagesRequest, out *BulkDeletePagesResponse) error
		ListPages(ctx context.Context, in *ListPagesRequest, out *ListPagesResponse) error
		ListPosts(ctx context.Context, in *ListPostsRequest, out *ListPostsResponse) error
	}
	type FBPageService struct {
		fBPageService
	}
	h := &fBPageServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&FBPageService{h}, opts...))
}

type fBPageServiceHandler struct {
	FBPageServiceHandler
}

func (h *fBPageServiceHandler) SendMessage(ctx context.Context, in *SendMessageRequest, out *SendMessageResponse) error {
	return h.FBPageServiceHandler.SendMessage(ctx, in, out)
}

func (h *fBPageServiceHandler) RegisterPage(ctx context.Context, in *RegisterPageRequest, out *RegisterPageResponse) error {
	return h.FBPageServiceHandler.RegisterPage(ctx, in, out)
}

func (h *fBPageServiceHandler) BulkDeletePages(ctx context.Context, in *BulkDeletePagesRequest, out *BulkDeletePagesResponse) error {
	return h.FBPageServiceHandler.BulkDeletePages(ctx, in, out)
}

func (h *fBPageServiceHandler) ListPages(ctx context.Context, in *ListPagesRequest, out *ListPagesResponse) error {
	return h.FBPageServiceHandler.ListPages(ctx, in, out)
}

func (h *fBPageServiceHandler) ListPosts(ctx context.Context, in *ListPostsRequest, out *ListPostsResponse) error {
	return h.FBPageServiceHandler.ListPosts(ctx, in, out)
}
