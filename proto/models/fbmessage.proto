syntax = "proto3";

package exmsg.models;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/models;models";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

message OutboundMessage {
    optional string text = 1;
    optional Attachment attachment = 2;
}

message Attachment {
    string type = 1;
    Payload payload = 2;
}

message Payload {
    string url = 1; // URL of the attachment
    
    // Whether the attachment can be reused (through attachment ID), currently not implemented
    // optional bool is_reusable = 2;
}
