syntax = "proto3";

package exmsg.services;

option go_package = "gitlab.com/a7923/athena-go/proto/exmsg/services;services";

import "models/fbmessage.proto";
import "google/protobuf/timestamp.proto";
import "models/fbpage.proto";
import "models/fbpost.proto";

service FBPageService {
  rpc SendMessage (SendMessageRequest) returns (SendMessageResponse);
  rpc RegisterPage (RegisterPageRequest) returns (RegisterPageResponse);
  rpc BulkDeletePages (BulkDeletePagesRequest) returns (BulkDeletePagesResponse);
  rpc ListPages(ListPagesRequest) returns (ListPagesResponse);

  rpc ListPosts(ListPostsRequest) returns (ListPostsResponse);
}

message SendMessageRequest {
  string page_id = 1;
  string recipient_id = 2;
  exmsg.models.OutboundMessage message = 3;
  string messaging_type = 4;
}

message SendMessageResponse {
  string recipient_id = 1;
  string message_id = 2;
}

message RegisterPageRequest {
  string page_id = 1;
  string page_name = 2;
  string page_access_token = 3;
  string app_secret = 4;
}

message RegisterPageResponse {
  google.protobuf.Timestamp created_at = 1;
}

message BulkDeletePagesRequest {
  repeated string page_ids = 1;
}

message BulkDeletePagesResponse {
}

message ListPagesRequest {
  int32 page = 1;
  int32 limit = 2;
  string sort_field = 3;
  bool is_asc = 4;
  optional string search_term = 5;
}

message ListPagesResponse {
  repeated exmsg.models.FBPage pages = 1;
  int64 total = 2;
}

message ListPostsRequest {
  int32 page = 1;
  int32 limit = 2;
  string sort_field = 3;
  bool is_asc = 4;
  string page_id = 5;
  optional string search_term = 6;
}

message ListPostsResponse {
  repeated exmsg.models.Post posts = 1;
}