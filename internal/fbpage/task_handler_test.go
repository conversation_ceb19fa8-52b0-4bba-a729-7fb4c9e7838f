//go:build integration
// +build integration

package fbpage

import (
	"context"
	"os"
	"testing"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
)

func TestHandleInsertPostsTask(t *testing.T) {
	dbURL := os.Getenv("DB_URL")
	if dbURL == "" {
		t.Skip("Skipping integration test: DB_URL environment variable must be set")
	}

	db, err := sqlx.ConnectContext(context.Background(), "postgres", dbURL)
	if err != nil {
		t.Fatalf("Failed to connect to database: %v", err)
	}

	handler := FBPageTaskHandler{
		db: db,
	}

	posts := []Post{
		{
			ID:          "1234567890",
			PageID:      "0987654321",
			TextContent: "Test post",
			URL:         "https://www.facebook.com/1234567890",
			Attachments: []byte("{}"),
		},
		{
			ID:          "1234567891",
			PageID:      "0987654321",
			TextContent: "Test post 2",
			URL:         "https://www.facebook.com/1234567891",
			Attachments: []byte(`{
				"data": [
					{
						"media_type": "PHOTO",
						"media_url": "https://www.facebook.com/1234567891/media/1"
					}
				]
			}`),
		},
	}
	t.Cleanup(func() {
		for _, post := range posts {
			_, err := db.ExecContext(context.Background(), "DELETE FROM posts WHERE id = $1", post.ID)
			if err != nil {
				t.Errorf("Failed to delete post: %v", err)
			}
		}
		db.Close()
	})

	testCases := []struct {
		testName string
		posts    []Post
	}{
		{
			testName: "Insert posts",
			posts:    posts,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.testName, func(t *testing.T) {
			task, err := NewInsertPostsTask(tc.posts)
			if err != nil {
				t.Errorf("Failed to create insert posts task: %v", err)
				return
			}

			err = handler.HandleInsertPostsTask(context.Background(), task)
			if err != nil {
				t.Errorf("Failed to handle insert posts task: %v", err)
				return
			}

			posts := make([]Post, 0)
			err = db.SelectContext(context.Background(), &posts, "SELECT id, text_content, url, attachments FROM posts")
			if err != nil {
				t.Errorf("Failed to query posts: %v", err)
				return
			}

			if len(posts) != len(tc.posts) {
				t.Errorf("Expected %d posts, got %d", len(tc.posts), len(posts))
				return
			}

			appear := make(map[string]bool)
			for _, post := range posts {
				appear[post.ID] = true
			}

			for _, post := range tc.posts {
				if !appear[post.ID] {
					t.Errorf("Post %s not found", post.ID)
				}
			}
		})
	}
}
