package fbpage

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

type PageFetcher struct {
	baseURL string
	client  *http.Client
}

type PageFetcherConfig struct {
	BaseURL string
	Client  *http.Client
}

var DefaultPageFetcherConfig = PageFetcherConfig{
	BaseURL: "https://graph.facebook.com/v22.0",
	Client: &http.Client{
		Timeout: 90 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 100,
			IdleConnTimeout:     90 * time.Second,
		},
	},
}

func NewPageFetcher(cfg *PageFetcherConfig) *PageFetcher {
	if cfg == nil {
		cfg = &DefaultPageFetcherConfig
	}
	return &PageFetcher{
		baseURL: cfg.BaseURL,
		client:  cfg.Client,
	}
}

func (f *PageFetcher) FetchPageInfo(ctx context.Context, token string) (*Page, error) {
	url := f.baseURL + "/me?access_token=" + token
	resp, err := f.client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, nil
	}

	var page Page
	if err := json.NewDecoder(resp.Body).Decode(&page); err != nil {
		return nil, err
	}

	page.AccessToken = token

	return &page, nil
}

type FetchPostsResult struct {
	Data     []DummyPost `json:"data"`
	NextPage string      `json:"next"`
}

func (f *PageFetcher) FetchPosts(ctx context.Context, pageID string, token string) ([]Post, error) {
	url := fmt.Sprintf("%s/%s/posts?fields=id,story,message,permalink_url,attachments&limit=50&access_token=%s",
		f.baseURL, pageID, token,
	)
	resp, err := f.client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, nil
	}

	var fetchResult FetchPostsResult
	if err := json.NewDecoder(resp.Body).Decode(&fetchResult); err != nil {
		return nil, err
	}
	posts := make([]Post, len(fetchResult.Data))
	for i := range fetchResult.Data {
		posts[i] = convertDummyPostToPost(fetchResult.Data[i])
	}

	for i := range posts {
		posts[i].PageID = pageID
	}

	return posts, nil
}
