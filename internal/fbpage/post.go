package fbpage

import "encoding/json"

type Post struct {
	ID          string          `json:"id"`
	PageID      string          `json:"page_id"`
	TextContent string          `json:"text_content" db:"text_content"`
	URL         string          `json:"url"`
	Attachments json.RawMessage `json:"attachments"`
}

type DummyPost struct {
	ID           string          `json:"id"`
	Message      string          `json:"message"`
	Story        string          `json:"story"`
	PermalinkURL string          `json:"permalink_url"`
	Attachments  json.RawMessage `json:"attachments"`
}

func convertDummyPostToPost(dummyPost DummyPost) Post {
	p := Post{
		ID:          dummyPost.ID,
		URL:         dummyPost.PermalinkURL,
		Attachments: dummyPost.Attachments,
	}

	// TextContent could be in field "message" or "story"
	// which depends on the post type
	if dummyPost.Message != "" {
		p.TextContent = dummyPost.Message
	} else {
		p.TextContent = dummyPost.Story
	}

	return p
}
