package fbpage

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/Masterminds/squirrel"
	"github.com/hibiken/asynq"
	"github.com/jmoiron/sqlx"
	"gitlab.com/a7923/athena-go/pkg/logger"
)

// FBPageTaskHandler handles tasks related to Facebook pages
type FBPageTaskHandler struct {
	asynqClient *asynq.Client
	db          *sqlx.DB
	fetcher     *PageFetcher
}

func NewFBPageTaskHandler(asynqClient *asynq.Client, db *sqlx.DB, fetcher *PageFetcher) *FBPageTaskHandler {
	return &FBPageTaskHandler{
		asynqClient: asynqClient,
		db:          db,
		fetcher:     fetcher,
	}
}

func (h *FBPageTaskHandler) HandleFetchPostsTask(ctx context.Context, task *asynq.Task) error {
	atlogger := logger.FromCtx(ctx)

	payload := FetchPostsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	posts, err := h.fetcher.FetchPosts(ctx, payload.PageID, payload.AccessToken)
	if err != nil {
		atlogger.Errorw("Failed to fetch posts",
			"page_id", payload.PageID,
			"error", err.Error())
		return err
	}

	insertTask, err := NewInsertPostsTask(posts)
	if err != nil {
		atlogger.Errorw("Failed to create insert posts task",
			"page_id", payload.PageID,
			"error", err.Error())
		return err
	}
	if _, err := h.asynqClient.Enqueue(insertTask, asynq.Queue("insert"), asynq.MaxRetry(3)); err != nil {
		atlogger.Errorw("Failed to enqueue insert posts task",
			"page_id", payload.PageID,
			"error", err.Error())
		return err
	}

	return nil
}

func (h *FBPageTaskHandler) HandleInsertPostsTask(ctx context.Context, task *asynq.Task) error {
	atlogger := logger.FromCtx(ctx)

	payload := InsertPostsPayload{}
	if err := json.Unmarshal(task.Payload(), &payload); err != nil {
		return err
	}

	queryBuilder := squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar)
	query := queryBuilder.Insert("posts").
		Columns(
			"id",
			"text_content",
			"url",
			"attachments",
			"page_id",
		)
	for _, post := range payload.Posts {
		query = query.Values(
			post.ID,
			post.TextContent,
			post.URL,
			post.Attachments,
			post.PageID,
		)
	}

	query = query.Suffix(`
		ON CONFLICT (id) DO UPDATE
		SET text_content = EXCLUDED.text_content,
			url = EXCLUDED.url,
			attachments = EXCLUDED.attachments,
			updated_at = NOW()
	`)

	sql, args, err := query.ToSql()
	if err != nil {
		atlogger.Errorw("Failed to build query",
			"error", err.Error())
		return err
	}

	_, err = h.db.ExecContext(ctx, sql, args...)
	if err != nil {
		atlogger.Errorw("Failed to execute query",
			"query", sql,
			"args", fmt.Sprintf("%+v", args),
			"error", err.Error(),
		)
		return err
	}

	return nil
}
