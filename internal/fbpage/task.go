package fbpage

import (
	"encoding/json"

	"github.com/hibiken/asynq"
)

const (
	TypeFetchPosts = "fetch:posts"

	TypeInsertPosts = "insert:posts"
)

type FetchPostsPayload struct {
	PageID      string
	AccessToken string
}

func NewFetchPostsTask(pageID string, token string) (*asynq.Task, error) {
	payload, err := json.Marshal(FetchPostsPayload{PageID: pageID, AccessToken: token})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(string(TypeFetchPosts), payload), nil
}

type InsertPostsPayload struct {
	Posts []Post
}

func NewInsertPostsTask(posts []Post) (*asynq.Task, error) {
	payload, err := json.Marshal(InsertPostsPayload{Posts: posts})
	if err != nil {
		return nil, err
	}
	return asynq.NewTask(string(TypeInsertPosts), payload), nil
}
