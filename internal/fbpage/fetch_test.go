//go:build integration
// +build integration

package fbpage

import (
	"context"
	"os"
	"testing"
)

func TestFetchPageInfo(t *testing.T) {
	testToken := os.Getenv("FB_PAGE_ACCESS_TOKEN")
	if testToken == "" {
		t.Skip("Skipping integration test: FB_ACCESS_TOKEN environment variable must be set")
	}

	fetcher := NewPageFetcher(nil)
	ctx := context.Background()

	page, err := fetcher.FetchPageInfo(ctx, testToken)
	if err != nil {
		t.Fatalf("Failed to fetch page info: %v", err)
	}
	t.Logf("page: %+v", page)
}

func TestFetchPosts(t *testing.T) {
	testToken := os.Getenv("FB_PAGE_ACCESS_TOKEN")
	pageID := os.Getenv("FB_PAGE_ID")
	if testToken == "" || pageID == "" {
		t.<PERSON><PERSON>("Skipping integration test: FB_PAGE_ACCESS_TOKEN and FB_PAGE_ID environment variables must be set")
	}

	fetcher := NewPageFetcher(nil)
	ctx := context.Background()

	posts, err := fetcher.FetchPosts(ctx, pageID, testToken)
	if err != nil {
		t.Fatalf("Failed to fetch posts: %v", err)
	}

	for _, post := range posts {
		t.Logf("Post: ID: %s, PageID: %s, TextContent: %s, URL: %s, Attachments: %s\n",
			post.ID, post.PageID, post.TextContent, post.URL, post.Attachments)
	}
}
